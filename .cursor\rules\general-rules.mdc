---
description: 
globs: 
alwaysApply: true
---
---
description:  
globs:  
alwaysApply: true
---

# **General Rules**

* Please reply in Chinese in the chat window, but all articles to be saved must be written in English.

# **Execution Guidelines**

* Filenames must use only lowercase letters and underscores (no spaces or special characters allowed).  
* Steps marked with `[SKIP]` or `略过` should be skipped.  
* Steps marked as `[Required]` or `【必要】` must be executed.

# **MCP Tool Usage Standards**

## **Competitor Research & Web Search**

* Use `duckduckgo-mcp-server` to search with the specified `[keywords]`, and filter out irrelevant pages.  
* Backup tools: `firecrawl-mcp`, `tavily-mcp`.

## **Content Extraction**

* Use tools in the following order: `firecrawl-mcp`, `puppeteer`, `tavily-mcp`.  
* For Reddit content, use **Reddit Scraper Lite**.  
* You must attempt to extract content from all provided URLs. Only skip if all attempts fail.

## **YouTube Data**

* Use only the `youtube-data-mcp-server` tool.

## **Basic Search**

* Prioritize `duckduckgo-mcp-server`.  
* Backup tools: `firecrawl-mcp`, `tavily-mcp`, `exa-tools`.

## **Deep Research**

* Use `perplexity-mcp` (also known as `deep_research`) only after completing basic search.  
* If this tool is unavailable, use `firecrawl-mcp` or `tavily-mcp` as alternatives.

## **Image Processing**

* Use `gemini-image-generator`.

## **File System**

* All file creation and editing tasks should be completed using `desktop-commander`.

## **Word counting**
* Use `mcp-wordcounter` 检查文档字数的时候，必须使用绝对路径，比如["filePath": "C:\\Cursor_Project\\QQ_Music_to_MP3\\first_draft.md"]

