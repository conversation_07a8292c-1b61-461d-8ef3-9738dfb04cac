# 文章创作执行计划

## 用户需求和目标
- **文章主题**: Download from Spotify Downloader Telegram Bots
- **SEO关键词**: Download from Spotify Downloader Telegram Bots
- **文章长度**: 1600字（可超出20%，最多1920字）
- **语言**: 英文
- **时间框架**: 基于2025年6月的最新数据和趋势
- **目标受众**: 音乐爱好者和创作者，专注于下载、编辑和分享音乐以获得更好的音频体验
- **开头策略**: B - 修辞问句开头
- **推荐产品**: Cinch Audio Recorder Pro

## 四大内容质量评估维度
1. **Effort (努力程度)**: 体现明显的人工成分和深度思考
2. **Originality (原创性)**: 提供独特信息增量，避免"炒冷饭"
3. **Talent/Skill (专业能力)**: 展示作者在该领域的专业知识和实际经验
4. **Accuracy (准确性)**: 确保事实准确，避免错误信息

## 信息增量要求
- 包含至少3-5个其他文章未涵盖的独特观点或解决方案
- 基于实际使用经验的个人见解和试错故事
- 针对用户痛点的具体解决方案

## 执行步骤详细清单

### 步骤1: 基础研究和超级大纲生成
- [ ] 提取参考URL的H2-H4标题
- [ ] 合并整理类似标题
- [ ] 按层级结构重新组织
- [ ] 保存至 `super_outline.md`

### 步骤2: 竞品内容分析
- [ ] 深度分析Google搜索结果前10名文章
- [ ] 识别现有内容的3-5个不足或遗漏点
- [ ] 记录Reddit、Quora等平台未解决的问题
- [ ] 挖掘独特价值点

### 步骤3: 最终大纲创建
- [ ] 整合超级大纲和竞品分析结果
- [ ] 优化大纲结构
- [ ] 分配各章节字数（总计1600字）
- [ ] 添加SEO关键词列表
- [ ] 保存至 `final_outline.md`

### 步骤4: 初稿撰写
- [ ] 基于最终大纲撰写文章
- [ ] 确保人工经验要素融入
- [ ] 遵循拟人化写作风格
- [ ] 适当整合Cinch Audio Recorder推荐
- [ ] 保存至 `first_draft.md`

### 步骤5: SEO内容生成
- [ ] 创建SEO标题和元描述
- [ ] 生成featured image提示词
- [ ] 保存至 `seo_metadata_images.md`

### 步骤6: 质量检查
- [ ] 验证字数是否在1600-1920字范围内
- [ ] 检查拟人化写作风格
- [ ] 识别并修正AI语言痕迹
- [ ] 验证内链和外链有效性
- [ ] 确认相关图片添加

## 完成标准和检查点

### 内容质量标准
- 包含至少3个竞品文章未涵盖的独特观点
- 每个H2章节包含人工经验要素
- 识别并解决用户具体痛点
- 包含可验证的准确信息和数据
- 体现作者专业判断和建议

### 字数分配标准（基于1600字）
- Introduction: 128字 (8%)
- 核心推荐章节(Cinch Audio Recorder): 320-400字 (20-25%)
- 主要内容章节(Telegram Bot分析): 560-640字 (35-40%)
- 支撑章节(背景/故障排除/最佳实践): 400-480字 (25-30%)
- Conclusion + FAQ: 160-192字 (10-12%)

## 预期输出文件清单
1. `super_outline.md` - 基础超级大纲
2. `final_outline.md` - 最终优化大纲
3. `first_draft.md` - 完整文章初稿
4. `seo_metadata_images.md` - SEO元数据和图片提示词

## 参考资源
- 用户需求文件: `New_article/info_aia.md`
- 产品指南: `New_article/car_guide.md`
- 拟人化写作指南: `New_article/hl.md`
- 参考URL:
  - https://www.viwizard.com/spotify-music-tips/download-from-spotify-telegram-bot.html
  - https://www.drmare.com/spotify-music/download-from-spotify-telegram-bot.html
  - https://www.noteburner.com/spotify-music-tips/10+-best-spotify-telegram-bot.html
