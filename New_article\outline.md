# 第1部分：文章大纲生成

## 执行文章撰写计划
首先，你需要制定一个详细的执行计划。根据用户要求`C:\Cursor_Project\New_article\info_aia.md`的文章主题(Article Topic)创建同名文件夹`C:\Cursor_Project\{keyword}`下创建名为`plan.md`的计划文件，该文件应包含以下内容：
    1. 用户的具体需求和目标
    2. 需要执行的所有步骤的详细清单
    3. 每个步骤的完成标准和检查点
    4. 预期输出的文件清单

## 步骤1：基础的"超级大纲"生成
超级大纲执行步骤如下：

1. 从`info_aia.md`文件中提取所有参考URL的H2至H4级标题。
2. 合并整理提取的标题
   - 把类似标题合并（例如，"DRM如何工作"→"DRM基础"），并用*标记源数量
   - 将整理后的标题按照层级结构（H1→H2→H3→H4）重新组织，形成文章的初步框架大纲（**不包含引言和结论章节**）。
将合并后的初级大纲保存至`/{keyword}/super_outline.md`。

## 步骤2：创建最终的文章大纲
将优化后的文章大纲作为最终版本保存至`/{keyword}/final_outline.md`。

优化大纲步骤如下：
**整合并分析输入：** 根据`/{keyword}/super_outline.md`初级大纲和用户要求(New_article\info_aia.md)。

## 信息研究指令【确保内容优于已有文章】

### 🎯 核心目标：确保内容优于现有文章
基于Google最新质量标准，每篇文章必须提供明显的信息增量(Information Gain)。

### 执行任务：

1. **竞品内容空白分析**：
   - 深度分析Google搜索结果前10名文章的内容结构和覆盖范围
   - 识别现有内容的3-5个明显不足或遗漏点
   - 记录用户在Reddit、Quora等平台提出但主流文章未解决的问题

2. **独特价值点挖掘**：
   - 找出至少3个"我希望早知道的事"类型的实用建议
   - 识别初学者常犯但教程很少提及的错误
   - 发现专家级用户才知道的隐藏技巧或最佳实践

3. **人工经验要素**：
   - 为每个主要章节准备1-2个基于"实际使用"的微型案例
   - 设计试错经历和解决过程的叙述
   - 准备个人观点和主观判断的表达


4. 整个大纲结构应优先解答用户的核心问题和需求，然后是高级应该和优化等其他方面的标题。

5. 在大纲最后附加在研究过程中发现的**SEO NLP和长尾关键词**综合列表。这些应包括主要关键词和语义变体。

### 📊 内容质量检查清单
在完成大纲后，必须验证：
- [ ] 是否包含至少3个竞品文章未涵盖的独特观点？
- [ ] 是否为每个H2章节准备了人工经验要素？
- [ ] 是否识别并准备解决用户的具体痛点？
- [ ] 是否包含可验证的准确信息和数据？
- [ ] 是否体现了作者的专业判断和建议？

**Introduction、Conclusion和FAQS：**
根据要求完成这3个部分：
- **引言**（100字）：
根据用户在info_aia.md中选择的开头策略(A/B/C/D)来撰写文章引言。每种策略的具体要求如下：

  **Opening Strategy Options:**
  **A. Surprising Statistic/Fact Opening:**
  - Use when you discovered significant statistics or industry data during research
  - Best for: Articles about widespread problems, market trends, or surprising industry insights
  - Use current, verifiable statistics from your research (2024-2025 data preferred)

  **B. Rhetorical Question Opening:**
  - Use when your research revealed strong user frustrations or common complaints
  - Best for: Articles addressing widely discussed problems in forums
  - Base the question on actual user frustrations or common problems found in your research

  **C. "What if" Scenario Opening:**
  - Use when your article provides a clear transformation or significant improvement
  - Best for: Tutorial articles that promise specific outcomes
  - Present a solution-focused scenario based on the benefits your article delivers

  **D. Personal Experience/Case Study Opening:**
  - Use when you discovered interesting case studies or user experiences during research
  - Best for: Articles where real user experiences illustrate the problem/solution
  - Reference real scenarios from forums, reviews, or expert testimonials you discovered

  **E. Relatable Scenario Opening:**
  - Best for: Articles about common, everyday situations users face
  - Create a specific, realistic scenario that your target audience frequently encounters

- **结论**（150字）：
  总结要点，强调价值主张（例如，"现在你可以随时随地欣赏音乐"），并包含号召性用语（例如，"在评论中分享你的技巧"）。

- **FAQS**（150字）：
  保持3-5个常见问题，每个问题都以一句话简明回答。问题和答案分成2行，不要放在一行。问题和答案之间用空行隔开。

## **优先推荐解决方案**：
遵循`New_article/info_aia.md`中用户指定的产品推荐指南，确保适当的产品放置和推广。

## **文章整体字数控制**：【必要】
1. **自动读取字数范围**：从`New_article/info_aia.md`中提取文章总字数范围。

2. **智能字数分配算法**：
   根据总字数自动分配各章节字数，遵循以下比例：
   - Introduction: 100字
   - 核心推荐章节(推荐产品): 20-25%
   - 主要对比章节(工具对比/方法介绍): 35-40%
   - 支撑章节(背景说明/故障排除/最佳实践): 25-30%
   - Conclusion + FAQ: 200字

3. **字数分配验证**：
   - 计算所有章节字数分配的总和
   - 确保总和在目标字数范围内(不超过上限的95%)
   - 如超出，按重要性优先级自动调整各章节分配

4. **标注格式**：
   在每个H2标题后标注：`(目标字数: XXX-XXX words)`
   在每个H3标题后标注：`(目标字数: XXX words)`

5. **通用字数分配公式**：
   设用户要求的最高字数为 T 
   - Introduction: T × 8-10%
   - 核心推荐章节: T × 20-25%
   - 主要内容章节: T × 35-40%
   - 支撑章节: T × 25-30%
   - Conclusion + FAQ: T × 8-12%

6. **自动调整机制**：
   如分配总和超出目标上限95%，按以下优先级缩减：
   1. 支撑章节 (-10%)
   2. 主要内容章节 (-5%)
   3. 核心推荐章节 (保持不变)

7. **执行检查**：
   生成大纲后，必须验证：
   - [ ] 所有章节字数分配总和是否在目标范围内
   - [ ] 核心推荐产品章节是否获得足够字数分配(20-25%)
   - [ ] 是否为每个标题标注了具体字数目标
   - [ ] 字数分配是否合理，避免后续需要大幅调整

严格遵守所有章节字数总和不超过用户限制(超过就必须要重新调整章节字数)。在每个标题后标注具体字数目标，便于后续内容创作时参考。
- 各章节分配总和：[计算结果] words
- 状态：✅符合/❌超出 目标范围