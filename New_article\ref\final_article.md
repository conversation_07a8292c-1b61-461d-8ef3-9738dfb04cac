## 步骤5：优化初稿形成最终版文章
核心目标：检查（不在文章中显示检查结果）并优化初稿文章（`/{keyword}/first_draft.md`）并形成最终版的文章，确保最终文章内容符合真人写作习惯，并检查语法、拼写、标点符号、排版、逻辑、准确性、链接有效性、内部链接有效性、格式、语气、可读性、初学者友好性。

1. **模仿人类写作检查：**【必要】
   首先根据`New_article\hl.md`指南审查`/{keyword}/first_draft.md`中的所有章节。

    **再次重点检查以下步骤进行并行修改：**
   [ ] Introduction部分是否按用户选择的类型来写的？
   [ ] H2-H4标题是否人性化、吸引人？
   [ ] 短句是否够多？长短句搭配是否自然？
   [ ] 对话短语用得是否自然、不刻意？
   [ ] 有无真情实感、个人色彩？
   [ ] 是否完全避免了禁用词和AI常用短语？
   [ ] 语气是否从头到尾都友好自然，像聊天一样？
   [ ] 阅读起来节奏是否流畅？
   [ ] 整体感觉是否像一个真实的朋友在热心分享，而不是AI模仿？
   [ ] 如果感觉生硬，尝试增加更短的句子、口语化表达或个人感悟。
   [ ] 结尾是否引导读者互动（例如：“你怎么看？”、“欢迎留言分享你的经验！”）？

2. **初学者友好验证清单：** 【必要】
   根据以下的清单审查`/{keyword}/first_draft.md`中的所有章节
   - [ ] 包含至少1个"其他地方未提及的教程细节"或"初学者陷阱警告"（来自原始或新价值维度）
   - [ ] 警告1个"常见初学者错误"并提供解决方案（可能来自"关键'不要做什么'"）
   - [ ] 提供"最简单但足够"的解决方案（可能来自"最低可行理解"或"简化解决方案"）
   - [ ] 解释"为什么这样做"而不仅仅是"如何做"
   - [ ] 解决初学者关注点（如"难吗？"，"免费版本够用吗？"，"安全吗？"，"需要多长时间？"）
   - [ ] 包含至少2个基于实际使用经验的具体提示
   - [ ] 使用具体数据或示例展示实际结果
   - [ ] 提供基于场景的使用建议（来自原始或"证明努力的价值"）
    **内容可视化检查：**【必要】
   在完成文章写作后，必须回顾全文并检查以下场景：
   - [ ] 是否有3个以上产品/服务需要对比？如有，必须添加比较表格
   - [ ] 是否有复杂的技术规格需要展示？如有，考虑使用表格
   - [ ] 是否有多个选项的优缺点？如有，使用表格对比
   - [ ] 是否有价格信息需要对比？如有，使用表格展示
   - [ ] 是否有评分或推荐级别？如有，使用星级或评分系统
   - [ ] 表格是否包含了用户最关心的决策因素？
   - [ ] 表格信息是否准确且易于理解？

3. **对文章进行全面的编辑审查：**【必要】
    根据以下的清单审查`/{keyword}/first_draft.md`中的所有章节
   - [ ] 检查文章中的语法、拼写、标点符号和排版错误
   - [ ] 验证逻辑流程和一致性，同时消除冗余、重叠或离题内容
   - [ ] 检查是否按要求添加了外链和内部链接
      - [ ] 检查外部链接是否有效（例如，产品官网、技术文档、博客文章）并通过搜索官方网站或权威网站确保文章中的出现的价格和技术细节的准确性。
      - [ ] 检查文章中的内部链接是否指向正确的页面, 页面是否能在网站地图找到(https://www.cinchsolution.com/sitemap/)。
   - [ ] 检查是否有重复的事实。 比如同一个事实或则数据在多个地方出现。
   - [ ] **强制图片检查**：检查添加的图片是否按要求获取
      - [ ] 推荐产品：只能使用指定的官方图片
      - [ ] 其他产品/章节：必须通过Google图片搜索添加相关插图
      - [ ] 每个重要H2章节都应该有配套图片
      - [ ] 图片链接有效且相关度高
   - [ ] **强制外链检查**：检查所有产品/服务名称是否添加外链
      - [ ] 列出文章中所有产品名称：_____________
      - [ ] 逐一验证每个产品是否有对应外链
      - [ ] 检查外链是否指向正确的官方网站
   - [ ] 检查文章中的是否添加网站内部链接，并验证内部链接是否指向正确的页面。
   - [ ] 检查检查推荐产品的介绍和步骤是否按要求获取产品信息并确定是否正确添加了指定的产品图片和下载链接
   - [ ] 所有Markdown标题（例如，##，### 标题）必须单独成行，前后有空行



将优化过的终文章保存至`/{keyword}/final_article.md`。
