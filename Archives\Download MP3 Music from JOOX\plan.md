# Article Creation Plan: Download MP3 Music from JOOX

## User Requirements and Goals
Based on `info_aia.md`, the user requires:

### Article Specifications
- **Topic**: Download MP3 Music from JOOX
- **SEO Keywords**: Download MP3 Music from JOOX
- **Length**: 1600 Words (minimum, can exceed by up to 20%)
- **Language**: English
- **Timeframe**: June 2025 content with latest data and trends
- **Target Audience**: Music lovers and creators focused on downloading, editing, and sharing music

### Content Quality Requirements
- **Effort**: Must show obvious human elements and deep thinking
- **Originality**: Provide unique information increment, avoid rehashing existing content
- **Talent/Skill**: Demonstrate professional knowledge and practical experience
- **Accuracy**: Ensure factual accuracy, avoid misinformation

### Information Increment Requirements
- Include at least 3-5 unique viewpoints not covered by other articles
- Personal insights and trial-and-error stories based on actual usage experience
- Specific solutions for user pain points, not generic discussions

### Recommended Product
- **Cinch Audio Recorder** (Primary recommendation)
- Official page: https://www.cinchsolution.com/cinch-audio-recorder/
- Must reference `car_guide.md` for all product information and usage methods

## Detailed Execution Steps

### Step 1: Research and Analysis
1. Extract H2-H4 headings from reference URLs:
   - https://www.viwizard.com/record-audio/download-music-from-joox.html
   - https://www.drmare.com/audio-recorder/rip-music-from-joox.html
   - https://www.tuneskit.com/record-audio/record-audio-from-joox.html
2. Analyze competitor content gaps and identify unique value propositions
3. Research user pain points and unaddressed questions

### Step 2: Super Outline Creation
1. Merge and organize extracted headings
2. Create hierarchical structure (H1→H2→H3→H4)
3. Save as `super_outline.md`

### Step 3: Final Outline Optimization
1. Integrate super outline with user requirements
2. Apply word count allocation algorithm (1600 words target)
3. Add SEO keywords and long-tail variations
4. Include content quality checkpoints
5. Save as `final_outline.md`

### Step 4: First Draft Creation
1. Follow `first_draft.md` workflow
2. Write comprehensive article based on final outline
3. Integrate Cinch Audio Recorder recommendations appropriately
4. Save as `first_draft.md`

### Step 5: SEO Content Generation
1. Follow `seo_titles.md` workflow
2. Create SEO titles and meta descriptions
3. Generate featured image prompts
4. Save as `seo_metadata_images.md`

## Completion Standards and Checkpoints

### Content Quality Checklist
- [ ] At least 3 unique viewpoints not covered by competitors
- [ ] Human experience elements for each H2 section
- [ ] Specific user pain point solutions
- [ ] Verifiable accurate information and data
- [ ] Professional judgment and recommendations

### Word Count Allocation (Target: 1600 words)
- Introduction: 128-160 words (8-10%)
- Core recommendation section: 320-400 words (20-25%)
- Main comparison section: 560-640 words (35-40%)
- Supporting sections: 400-480 words (25-30%)
- Conclusion + FAQ: 128-192 words (8-12%)

### Technical Requirements
- Opening Strategy: A (Surprising Statistic/Fact)
- Include proper Cinch Audio Recorder integration
- Provide download links and buttons
- Use specified product images
- Include FAQ section (3-5 questions)

## Expected Output Files
1. `plan.md` - This execution plan ✓
2. `super_outline.md` - Initial merged outline from competitor analysis
3. `final_outline.md` - Optimized final outline with word allocations
4. `first_draft.md` - Complete article draft
5. `seo_metadata_images.md` - SEO titles, descriptions, and image prompts

## Success Metrics
- Article length: 1600+ words (up to 1920 words acceptable)
- Unique value propositions: 3-5 identified and integrated
- User pain points addressed: All major JOOX download challenges
- Product integration: Natural and helpful Cinch Audio Recorder recommendations
- SEO optimization: Comprehensive keyword integration and meta content
