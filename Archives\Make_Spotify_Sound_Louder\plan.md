# 文章创作执行计划

## 用户具体需求和目标
- **文章主题**: Make Spotify Sound Louder
- **SEO关键词**: Make Spotify Sound Louder
- **文章长度**: 1600字（不能少，最多可超出20%，即最高1920字）
- **语言**: 英文
- **时间框架**: 基于2025年6月的最新数据和趋势
- **目标受众**: 音乐爱好者和创作者，专注于下载、编辑和分享音乐以获得更好的音频体验
- **开头策略**: D - Personal Experience/Case Study Opening
- **推荐产品**: Cinch Audio Recorder Pro

## 需要执行的所有步骤详细清单

### 步骤1: 生成超级大纲
- 提取参考URL的H2-H4标题
- 合并整理类似标题
- 按层级结构组织初步框架
- 保存为 `super_outline.md`

### 步骤2: 创建最终大纲
- 基于超级大纲进行优化
- 进行竞品内容空白分析
- 挖掘独特价值点
- 添加人工经验要素
- 智能字数分配（1600字目标）
- 保存为 `final_outline.md`

### 步骤3: 撰写初稿
- 遵循拟人化写作指令（hl.md）
- 严格控制各章节字数
- 整合SEO长尾关键词
- 实施Google E-E-A-T标准
- 添加产品推荐和下载链接
- 为H2章节添加相关图片
- 保存为 `first_draft.md`

### 步骤4: 生成SEO内容
- 创建5组SEO标题和元描述
- 生成特色图片提示词
- 保存为 `seo_metadata_images.md`

### 步骤5: 最终检查
- 验证字数控制在1600-1920字范围
- 确认拟人化写作要求执行
- 检查所有步骤完整性

## 每个步骤的完成标准和检查点

### 步骤1完成标准:
- [ ] 成功提取所有参考URL的标题
- [ ] 合并类似标题并标记源数量
- [ ] 形成层级结构大纲
- [ ] 保存super_outline.md文件

### 步骤2完成标准:
- [ ] 包含至少3个竞品未涵盖的独特观点
- [ ] 为每个H2章节准备人工经验要素
- [ ] 字数分配总和在1520字以内（95%上限）
- [ ] 每个标题标注具体字数目标
- [ ] 保存final_outline.md文件

### 步骤3完成标准:
- [ ] 严格遵循各章节字数限制
- [ ] 体现拟人化写作风格
- [ ] 整合推荐产品和下载链接
- [ ] 为H2章节添加相关图片
- [ ] 总字数控制在1600-1920字
- [ ] 保存first_draft.md文件

### 步骤4完成标准:
- [ ] 生成5组符合长度要求的SEO标题和元描述
- [ ] 创建特色图片提示词
- [ ] 保存seo_metadata_images.md文件

### 步骤5完成标准:
- [ ] 字数精确控制在要求范围
- [ ] 拟人化写作要求完全执行
- [ ] 所有步骤和要求完整实施

## 预期输出文件清单
1. `super_outline.md` - 初级大纲
2. `final_outline.md` - 最终优化大纲
3. `first_draft.md` - 文章初稿
4. `seo_metadata_images.md` - SEO内容和图片提示词
5. `plan.md` - 本执行计划文件

## 质量控制要求
- 四大内容质量评估维度：努力程度、原创性、专业能力、准确性
- 信息增量：至少3-5个独特观点或解决方案
- 搜索体验优化：完整满足用户搜索意图
- 产品推荐：自然整合Cinch Audio Recorder Pro