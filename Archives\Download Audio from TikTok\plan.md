# 文章创作执行计划

## 用户需求和目标
- **文章主题**: Download Audio from TikTok
- **SEO关键词**: Download Audio from TikTok
- **文章长度**: 1600字（不能少，最多可超出20%，即最高1920字）
- **语言**: 英文
- **时间框架**: 基于2025年6月的最新数据和趋势
- **目标受众**: 音乐爱好者和创作者，专注于下载、编辑和分享音乐以获得更好的音频体验
- **推荐产品**: Cinch Audio Recorder Pro ($25.99)

## 四大内容质量评估维度
1. **Effort (努力程度)**: 体现明显的人工成分和深度思考
2. **Originality (原创性)**: 提供独特信息增量，避免"炒冷饭"
3. **Talent/Skill (专业能力)**: 展示作者在该领域的专业知识和实际经验
4. **Accuracy (准确性)**: 确保事实准确，避免错误信息

## 信息增量要求
- 包含至少3-5个其他文章未涵盖的独特观点或解决方案
- 基于实际使用经验的个人见解和试错故事
- 针对用户痛点的具体解决方案

## 详细执行步骤清单

### 步骤1: 基础研究和超级大纲生成
- [ ] 提取参考URL的H2-H4标题
- [ ] 合并整理类似标题
- [ ] 按层级结构重新组织
- [ ] 保存为 `super_outline.md`

### 步骤2: 竞品分析和内容空白识别
- [ ] 分析Google搜索结果前10名文章
- [ ] 识别现有内容的3-5个不足点
- [ ] 记录Reddit、Quora等平台未解决的问题
- [ ] 挖掘独特价值点

### 步骤3: 最终大纲优化
- [ ] 整合超级大纲和用户要求
- [ ] 添加人工经验要素
- [ ] 智能字数分配（1600字目标）
- [ ] 保存为 `final_outline.md`

### 步骤4: 初稿创作
- [ ] 遵循 `first_draft.md` 工作流程
- [ ] 使用最终大纲创作内容
- [ ] 保存为 `first_draft.md`

### 步骤5: SEO内容生成
- [ ] 遵循 `seo_titles.md` 工作流程
- [ ] 创建SEO标题和元描述
- [ ] 生成featured image提示词
- [ ] 保存为 `seo_metadata_images.md`

## 完成标准和检查点

### 大纲阶段检查点
- [ ] 包含至少3个竞品文章未涵盖的独特观点
- [ ] 为每个H2章节准备人工经验要素
- [ ] 识别并准备解决用户具体痛点
- [ ] 包含可验证的准确信息和数据
- [ ] 体现作者的专业判断和建议

### 字数分配检查点
- [ ] Introduction: 100字
- [ ] 核心推荐章节(Cinch Audio Recorder): 20-25% (320-400字)
- [ ] 主要对比章节: 35-40% (560-640字)
- [ ] 支撑章节: 25-30% (400-480字)
- [ ] Conclusion + FAQ: 200字
- [ ] 总字数控制在1600-1920字范围内

### 最终质量检查点
- [ ] 字数精确控制在要求范围内
- [ ] 按照拟人化写作要求撰写
- [ ] 完整执行所有步骤和要求
- [ ] 产品推荐自然融入内容
- [ ] SEO关键词合理分布

## 预期输出文件清单
1. `plan.md` - 执行计划（本文件）
2. `super_outline.md` - 初级大纲
3. `final_outline.md` - 最终优化大纲
4. `first_draft.md` - 文章初稿
5. `seo_metadata_images.md` - SEO内容和图片提示词

## 开头策略
选择策略D: Personal Experience/Case Study Opening
- 基于研究中发现的有趣案例或用户体验
- 适合展示问题/解决方案的真实场景
- 引用论坛、评论或专家证言中发现的真实情况
