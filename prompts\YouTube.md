## 🎯 任务目标

请根据我提供的竞争对手视频脚本（cs.md）的**结构、顺序和风格**，模仿写一个英文的 YouTube 视频脚本，内容基于我产品 Cinch Audio Recorder 的文档（New_article\car_guide.md）。在写脚本的时候必须参考拟人化写作（New_article\hl.md）确保内容符合拟人化风格。

---

## 📌 脚本要求

- **语言**：英文
- **长度**：与竞争对手脚本接近（约 125–140 单词/分钟，控制总时长一致）
- **风格**：模仿竞争对手脚本的讲解顺序、用词风格与段落节奏
- **用途**：适用于配音讲解，可与产品演示视频配合使用
- **结构清晰**，适合后期剪辑

---

## 🚀 产品优势必须突出以下几点

- 支持**所有流媒体平台**（不仅限 Spotify，如 Apple Music、Amazon Music 等）
- 无需虚拟声卡，操作简单(更简单安装）)
- 无需登录个人账号（避免因 API 授权造成账号封号风险）
- 提供 ID3 标签信息，但不依赖 Spotify API
- 软件使用方便、安全、稳定
- 有技术客服支持

---

## 🎬 视频结尾需加入 CTA（号召性用语）

请在视频结尾加入一句清晰的 Call to Action，引导观众点击视频的下链接了解详情：

- 官方网站：https://www.cinchsolution.com  
- 产品页：https://www.cinchsolution.com/cinch-audio-recorder/

---

## 👥 目标受众

所有流媒体音乐用户（不限平台）

---

## ✅ 输出格式

完整的英文视频脚本(md格式)
- 自然口语化，适合配音讲解
- 按段落组织，语速合理，便于剪辑与展示
- 文件名根据cs.md核心关键字命名
脚本大纲
- 视频标题
- 视频描述
- 每个段落预估音频时长，和总时长
- 需要录屏的电脑操作列表（比如录制在Windows电脑中如何移动文件夹从C盘到D盘）

