---
description: 
globs: 
alwaysApply: true
---
# **通用规则**

* 在聊天窗口中使用中文回复
* 除非用户有特别要求，所有保存文件的文章和文档均应使用英文撰写

# **执行指南**

* 文件名应仅使用小写字母和下划线（不允许空格或特殊字符）
* 包含 `[SKIP]`、`略过`  的步骤应跳过
* 标记为 `[Required]`或者`【必要】` 的步骤必须执行

# **MCP 工具使用规范**

## **竞品调研与网页搜索**

* 使用 `duckduckgo-mcp-server` 搜索 `[关键词]`，过滤无关页面
* 备用工具：`firecrawl-mcp`、`tavily-mcp`

## **内容抓取**

* 按顺序使用：`fetch`、 `firecrawl-mcp`、`puppeteer`、`tavily-mcp` 
* 若为 Reddit 内容，使用 **Reddit Scraper Lite**
* 必须尝试抓取所有提供的 URL，仅在全部失败时可跳过

## **YouTube 数据**

* 仅使用 `youtube-data-mcp-server` 工具

## **基础搜索**

* 优先使用 `duckduckgo-mcp-server`
* 备用工具：`firecrawl-mcp`、`tavily-mcp`、`exa-tools`

当出现robot检测时，使用 `puppeteer`

## **Reddit高级搜索**
需要搜索Reddit内容时候，使用Reddit Scraper Lite

## **深入研究**
* 仅在完成基础搜索后使用 `perplexity-mcp`（即 `deep_research`）
* 若该工具不可用，备用工具为 `firecrawl-mcp` 或 `tavily-mcp`

## **图像处理**

* 使用 `gemini-image-generator`

## **文件系统**

* 所有文件创建与编辑任务均使用 `desktop-commander` 完成

## **字数检查**
* 使用 `mcp-wordcounter` 检查文档字数的时候，必须使用绝对路径，比如["filePath": "C:\\Cursor_Project\\QQ_Music_to_MP3\\first_draft.md"]

处理任务复杂考虑使用 `sequential-thinking`