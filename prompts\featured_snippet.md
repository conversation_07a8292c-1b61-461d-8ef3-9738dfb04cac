Act as an SEO expert specialising in semantic SEO. I want you to help me write the perfect featured snippet in NLP friendly language to outrank my competitor on Google.
The keyword targeted is "[Keyword]"
Current ranking featured snippet is:
"[Existing Snippet]"
Write the perfect featured snippet to outrank the current ranking featured snippet. Put the answer in quotes.
Follow these rules:
- Answer the question directly
- Answer the question restating the keyword in the answer
- Write in an NLP friendly way
- Write an information-dense answer
- Write concisely
- Make it similar in structure but more informative than my competitor
- max 35 words
