---
title: AI Prompt & Workflow Command Reference
description: Organized prompt instructions for SEO, writing, formatting, troubleshooting, and tool usage
---

# 🔧 MCP Tool Execution Guideline

- If a tool fails, **fix the tool**, test it, and **only proceed** if it works. If the issue persists, repeat the fix-test process.

---

# ✍️ Prompt Refinement

## **Refine Prompts**
- Analyze the following prompt idea: 
  - Rewrite the prompt for clarity and effectiveness  
  - Identify potential improvements or additions  
  - Refine the prompt based on identified improvements  
  - Present the final optimized prompt

## **Enhance Clarity & Coherence**
1. Explain this complex sentence in everyday language:  
2. Revise this passage for improved clarity:  
3. Improve the readability of this passage:  
4. Reframe this passage to make it easier to follow:  

## **Reduce Length**
- Rewrite a concise version of this passage:  
- Reduce this passage to its key points:  
- This is too long. Please reduce the amount of text by around 30%  
- Condense the instruction while maintaining effectiveness  

---

# 📝 Blog Writing

## **Blog Titles**
- As an SEO specialist, create 10 unique, search-intent-aligned titles for the blog post titled **“Beatport to MP3”**  
- Each title must be distinct, 60 characters or fewer, and avoid duplication with existing search results

## **Meta Description**
- As an SEO specialist, write a meta description within 160 characters for the title:  
  _"Best 6 Way to Record Amazon Prime/Unlimited Music As MP3”_

## **Write for Outline**
- You are a tech blog copywriter with 10 years of experience  
- Write the content strictly based on the given outline, using everyday language  
- Do not add any new sections or assumptions  
- Ensure all content is accurate, fact-checked, and credible (no citations or links)

## **Add Step Numbers**
- Add step numbers like “Step 1:, Step 2:” to these instructions  
- Do **not** add or change content

## **Comparison Chart**
- Compare the **Alternatives to Audials Music**  
- Use a chart including:  
  - Tool Name  
  - Relevant Use Case  
  - Output Quality  
  - Stability  
  - Pricing Tiers  
- Do not include citations or links

## **Write Troubleshooting**
- You are a tech blog copywriter with 10 years of experience  
- Write a troubleshooting section for this article  
- Use clear language; no citations or links

## **Write Introduction**
- Write an introduction in **100 words**, using everyday language  
- No citations or links

## **Write Conclusion**
- Write a conclusion in **100 words**, using everyday language  
- No citations or links

---

# 🖼️ Image Creation

1. Provide 3 prompt ideas for generating a featured image for this article  
2. Create 3 images in **16:9 ratio**

---

# ✅ Fact-Checking

1. You are an expert SEO copywriter  
   - Conduct thorough research and fact-checking before writing  
   - Avoid assumptions; only use credible, verified info (no citations or links)  
2. Incorporate the keyword phrase **“best streaming audio recorder for Windows”** into the following paragraph  
3. I’m unsure whether **[Fact]** is true — please check it and provide a source  
4. Rewrite this content using correct **markup format** with H2, H3, and H4 headings

---

# 🧠 Multi-Step Prompt (SEO Copywriting)

```prompt
You are an expert SEO copywriter. Please rate the quality of the content you've produced above on a scale of 1–10.
```

```prompt
Rewrite the content above so that it scores higher.
```

### Include the following in the improved version:
- Introduce the best alternatives to **AudiCable Audio Recorder**:
  - Leawo Music Recorder  
  - Audacity  
  - Allavsoft  
- For each tool, include:
  - A brief overview of what it is and does  
  - **Why It Stands Out**  
  - Pros and cons  
  - Pricing and availability  
  - Real user feedback (from Trustpilot or Reddit)  
  - Examples, tutorials, or testimonials showing user benefits  
- Use:
  - **H3** headings for each alternative  
  - **H4** for subtopics under each tool

---