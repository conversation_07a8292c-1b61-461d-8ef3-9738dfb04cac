# 文章创作执行计划

## 用户需求和目标
- **文章主题**: Download MP3 Music from My Mixtapez
- **SEO关键词**: Download MP3 Music from My Mixtapez
- **文章长度**: 1600字（不能少，最多可超出20%，即最多1920字）
- **语言**: 英文
- **时间框架**: 基于2025年6月的最新数据和趋势
- **目标受众**: 音乐爱好者和创作者，专注于下载、编辑和分享音乐以获得更好的音频体验
- **开头策略**: B（修辞问题开头）
- **推荐产品**: Cinch Audio Recorder Pro ($25.99)

## 四大内容质量评估维度
1. **Effort (努力程度)**: 体现明显的人工成分和深度思考
2. **Originality (原创性)**: 提供独特信息增量，避免"炒冷饭"
3. **Talent/Skill (专业能力)**: 展示作者在该领域的专业知识和实际经验
4. **Accuracy (准确性)**: 确保事实准确，避免错误信息

## 信息增量要求
- 包含至少3-5个其他文章未涵盖的独特观点或解决方案
- 基于实际使用经验的个人见解和试错故事
- 针对用户痛点的具体解决方案

## 详细执行步骤清单

### 第1步：基础研究和超级大纲生成
- [ ] 抓取参考URL内容并提取H2-H4标题
- [ ] 合并整理类似标题，标记源数量
- [ ] 按层级结构重新组织，形成初步框架
- [ ] 保存为 `super_outline.md`

### 第2步：竞品分析和内容空白识别
- [ ] 深度分析Google搜索结果前10名文章
- [ ] 识别现有内容的3-5个明显不足或遗漏点
- [ ] 记录Reddit、Quora等平台未解决的问题
- [ ] 找出至少3个"我希望早知道的事"类型建议

### 第3步：最终大纲优化
- [ ] 整合超级大纲和竞品分析结果
- [ ] 添加独特价值点和人工经验要素
- [ ] 设计试错经历和解决过程叙述
- [ ] 添加SEO NLP和长尾关键词列表
- [ ] 智能字数分配（总计1600字）
- [ ] 保存为 `final_outline.md`

### 第4步：初稿创作
- [ ] 按照最终大纲撰写完整文章
- [ ] 确保每个章节达到目标字数
- [ ] 集成Cinch Audio Recorder产品推荐
- [ ] 添加个人经验和试错故事
- [ ] 保存为 `first_draft.md`

### 第5步：SEO内容生成
- [ ] 创建SEO标题和元描述
- [ ] 生成featured image图片提示词
- [ ] 保存为 `seo_metadata_images.md`

## 完成标准和检查点

### 超级大纲检查点
- [ ] 是否提取了所有参考URL的标题？
- [ ] 是否合并了类似标题并标记源数量？
- [ ] 是否按层级结构重新组织？

### 最终大纲检查点
- [ ] 是否包含至少3个竞品文章未涵盖的独特观点？
- [ ] 是否为每个H2章节准备了人工经验要素？
- [ ] 是否识别并准备解决用户的具体痛点？
- [ ] 字数分配总和是否在1600字范围内？
- [ ] 核心推荐产品章节是否获得足够字数分配(20-25%)？

### 初稿检查点
- [ ] 文章总字数是否在1600-1920字范围内？
- [ ] 是否按照拟人化写作要求（hl.md）撰写？
- [ ] 是否完整执行first_draft.md所有步骤和要求？
- [ ] 产品推荐是否自然融入且符合指导原则？

## 预期输出文件清单
1. `plan.md` - 执行计划文件
2. `super_outline.md` - 基础超级大纲
3. `final_outline.md` - 最终优化大纲
4. `first_draft.md` - 完整文章初稿
5. `seo_metadata_images.md` - SEO内容和图片提示词

## 产品推荐策略
- 遵循模块化结构，灵活选用合适模块
- 先说明免费工具的基本功能
- 指出使用限制和不足
- 介绍Cinch Audio Recorder作为补充工具
- 强调真实使用场景和灵活性
- 保持解决问题导向，避免过度推销
