---
type: "always_apply"
---

* 文件名应仅使用小写字母和下划线（不允许空格或特殊字符）
* 包含 `[SKIP]`、`略过`  的步骤应跳过
* 标记为 `[Required]`或者`【必要】` 的步骤必须执行



## 🧭 **MCP 工具使用规范**

### ✅ **任务处理**

* 任务较复杂或需多步推理时，请优先使用 `sequential-thinking`。

---

### 🔍 **竞品调研与网页搜索**

* 默认使用：自带的搜索功能或者`duckduckgo-mcp-server` 搜索相关关键字。
* 若搜索失败或无有效结果，依次尝试备用工具：

  * `firecrawl-mcp`
  * `tavily-mcp`

---

### 🌐 **内容抓取（网页/文本）**

* 抓取流程按优先级顺序依次尝试：默认是使用自带的网页获取功能。

  1. `fetch`
  2. `firecrawl-mcp`
  3. `tavily-mcp`
* 若内容来自 **Reddit**，请使用专用工具：`Reddit Scraper Lite`。
* 若遇到机器人验证或反爬虫，请使用：`puppeteer`。
* **必须尝试抓取所有提供的 URL**，仅在全部尝试失败后，才可视为“不可抓取”。

---

### 📺 **YouTube 数据**

* 仅使用 `youtube-data-mcp-server` 工具处理 YouTube 相关数据任务。

---

### 🔎 **基础搜索任务**

* 默认使用：：自带的搜索功能或者`duckduckgo-mcp-server`
* 若无法获取有效结果，可尝试以下备用工具（按推荐顺序）：
  *. `tavily-mcp`
  *. `exa-tools`
  *. `firecrawl-mcp`
---

### 📚 **深入研究任务**

* 深度搜索仅在完成基础搜索后进行，每个任务 **最多调用一次** `perplexity-mcp`。
* 若 `perplexity-mcp` 不可用，或需进一步研究，可使用以下工具补充：

  * `tavily-mcp`
  * `exa-tools`
  * `firecrawl-mcp`
---

### 🎨 **图像处理**

* 所有图像搜索使用：`gemini-image-generator`。

---

### 📁 **文件系统操作**
* 所有本地文件的读取、创建、修改任务均使用：自带文件读取，修改功能 或者`desktop-commander`。

---

### 🔢 **文档字数检查**

* 使用 `mcp-wordcounter` 工具时，路径必须为**绝对路径**。
* 格式示例（使用双反斜杠）：
  `[FILEPATH] "C:\\Cursor_Project\\QQ_Music_to_MP3\\first_draft.md"`
  `[FILEPATH] "C:\\Cursor_Project\\QQ_Music_to_MP3\\first_draft.md"`