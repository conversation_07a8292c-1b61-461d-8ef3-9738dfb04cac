# 文章创作执行计划

## 用户需求和目标
- **文章主题**: Spotify Won't Work on Apple CarPlay
- **SEO关键词**: Spotify Won't Work on Apple CarPlay
- **文章长度**: 1600字（可超出20%，最多1920字）
- **语言**: 英文
- **时间框架**: 基于2025年6月的最新数据和趋势
- **目标受众**: 音乐爱好者和创作者，专注于下载、编辑和分享音乐以获得更好的音频体验
- **开头策略**: C - "What if" 场景开头
- **推荐产品**: Cinch Audio Recorder

## 需要执行的详细步骤清单

### 第一阶段：研究和大纲创建
1. **提取参考URL内容** - 分析竞品文章结构和内容覆盖范围
2. **创建超级大纲** - 合并参考文章的H2-H4标题，保存为super_outline.md
3. **优化最终大纲** - 基于用户需求和信息增量要求，保存为final_outline.md
4. **字数分配验证** - 确保各章节字数分配符合1600字要求

### 第二阶段：内容创作
5. **撰写初稿** - 基于final_outline.md创建first_draft.md
6. **内容质量检查** - 验证拟人化写作、信息增量、专业性
7. **字数精确控制** - 确保文章在1600-1920字范围内

### 第三阶段：SEO优化
8. **生成SEO元数据** - 创建标题、描述和图片提示词
9. **保存SEO内容** - 将所有SEO相关内容保存至seo_metadata_images.md

### 第四阶段：最终检查
10. **链接有效性验证** - 检查所有内外链是否有效
11. **图片配置检查** - 确认相关图片是否按要求添加
12. **AI语言检测** - 检查是否有明显AI语言和句子结构

## 完成标准和检查点

### 内容质量标准
- [ ] 包含至少3-5个其他文章未涵盖的独特观点
- [ ] 每个H2章节包含人工经验要素和试错故事
- [ ] 体现专业知识和实际使用经验
- [ ] 避免AI语言，使用拟人化写作风格

### 技术要求检查
- [ ] 字数控制在1600-1920字范围内
- [ ] 按照"What if"场景开头策略撰写引言
- [ ] 正确集成Cinch Audio Recorder产品推荐
- [ ] 包含有效的内链和外链
- [ ] 添加相关图片和alt标签

### SEO优化检查
- [ ] 主关键词"Spotify Won't Work on Apple CarPlay"合理分布
- [ ] 包含长尾关键词和语义变体
- [ ] 元标题和描述符合SEO最佳实践
- [ ] 图片提示词针对性强，避免通用库存照片

## 预期输出文件清单
1. `super_outline.md` - 初始大纲
2. `final_outline.md` - 最终优化大纲
3. `first_draft.md` - 文章初稿
4. `seo_metadata_images.md` - SEO元数据和图片提示词
5. `plan.md` - 本执行计划文件

## 信息增量要求
- 基于实际使用经验的个人见解和试错故事
- 针对用户痛点的具体解决方案
- 至少3-5个其他文章未涵盖的独特观点或解决方案
- 重点关注用户搜索意图的完整满足

## 产品集成策略
- 先说明官方功能或免费工具已能解决的需求
- 明确用户在使用过程中遇到的具体限制或不足
- 引入第三方工具时，强调它们是补充而非替代
- 聚焦真实的使用场景，说明用户为什么需要更多灵活性
