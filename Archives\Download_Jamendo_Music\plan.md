# Download Jamendo Music - 文章创作执行计划

## 用户需求和目标

### 基本信息
- **文章主题**: Download Jamendo Music
- **SEO关键词**: Download Jamendo Music for Free
- **目标字数**: 1600字（最多可超出20%，即最高1920字）
- **语言**: 英文
- **时间框架**: 基于2025年6月的最新数据和趋势
- **目标受众**: 音乐爱好者和创作者，专注于下载、编辑和分享音乐体验

### 参考资源
- https://www.viwizard.com/record-audio/download-jamendo-music.html
- https://recorder.itopvpn.com/blog/download-music-from-jamendo-1949
- https://www.videoconverterfactory.com/tips/jamendo-music-download.html

### 推荐产品
- **Cinch Audio Recorder Pro** ($25.99 USD)
- 官方页面: https://www.cinchsolution.com/cinch-audio-recorder/

## 执行步骤详细清单

### 步骤1: 基础"超级大纲"生成
- [ ] 提取所有参考URL的H2-H4级标题
- [ ] 合并整理类似标题，标记源数量
- [ ] 按层级结构重新组织，形成初步框架
- [ ] 保存至 `super_outline.md`

### 步骤2: 创建最终文章大纲
- [ ] 基于超级大纲进行优化
- [ ] 进行竞品内容空白分析
- [ ] 挖掘独特价值点（至少3个）
- [ ] 添加人工经验要素
- [ ] 智能字数分配（目标1600字）
- [ ] 保存至 `final_outline.md`

### 步骤3: 创作初稿
- [ ] 基于最终大纲撰写完整文章
- [ ] 确保字数在1600-1920字范围内
- [ ] 整合Cinch Audio Recorder产品推荐
- [ ] 保存至 `first_draft.md`

### 步骤4: 生成SEO内容
- [ ] 创建SEO标题和元描述
- [ ] 生成featured image图片提示词
- [ ] 保存至 `seo_metadata_images.md`

## 完成标准和检查点

### 内容质量标准
- [ ] 包含至少3-5个独特观点或解决方案
- [ ] 基于实际使用经验的个人见解
- [ ] 针对用户痛点的具体解决方案
- [ ] 准确的事实信息和数据

### 字数分配验证
- Introduction: 128-160 words (8-10%)
- 核心推荐章节: 320-400 words (20-25%)
- 主要对比章节: 560-640 words (35-40%)
- 支撑章节: 400-480 words (25-30%)
- Conclusion + FAQ: 128-192 words (8-12%)

### SEO优化要求
- [ ] 关键词自然分布
- [ ] 长尾关键词覆盖
- [ ] 用户搜索意图满足
- [ ] 内容结构支持复杂查询

## 预期输出文件清单

1. `plan.md` - 执行计划文件 ✅
2. `super_outline.md` - 初级大纲
3. `final_outline.md` - 最终优化大纲
4. `first_draft.md` - 完整文章初稿
5. `seo_metadata_images.md` - SEO元数据和图片资源

## 产品推荐策略

### Cinch Audio Recorder集成要点
- 从用户角度说明官方功能和免费工具的能力
- 明确指出使用过程中的具体限制
- 强调第三方工具作为补充而非替代
- 聚焦真实使用场景和灵活性需求
- 保持友好实用的语气，以解决问题为核心

### 必要资源
- Windows下载链接: https://www.cinchsolution.com/CinchAudioRecorder.exe
- Mac下载链接: https://www.cinchsolution.com/CinchAudioRecorderProMac.dmg
- 产品界面图片和使用指南图片（已在car_guide.md中列出）
