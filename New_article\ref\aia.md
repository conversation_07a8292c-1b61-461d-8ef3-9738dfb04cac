# 文章创作优化步骤指南

 [Required] 遵循以下所有步骤来生成所需文件。每一步对于确保高质量结果都至关重要。在开始任务前，请生成一个工作流程检查表，列出所有需要完成的步骤，并在每完成一个步骤后进行标记。

## 第1部分：文章大纲生成

### 步骤1：接收用户输入并明确目标

1. 接收并处理用户输入
   - 查看`New_article/info_aia.md`中的用户要求，记住所有用户要求
   - 定义明确的任务目标并制定执行步骤
2. 自动为所有文件创建`articles/{keyword}/`文件夹

### 步骤2："超级大纲"生成
将合并后的初级大纲保存至`articles/{keyword}/super_outline.md`。
超级大纲执行步骤如下：
1. 从`info_aia.md`中的每个参考URL提取所有的H2-H4标题
2. 合并整理提取的标题
   - 把类似标题合并（例如，"DRM如何工作"→"DRM基础"），并用*标记源数量
   - 然后将它们组织成逻辑层次结构（H1→H2→H3→H4）的初级大纲（**去除引言和结论部分**）。



### 步骤3：创建最终的文章大纲
将优化后的文章大纲作为最终版本保存至`articles/{keyword}/final_outline.md`。

优化步骤如下：
**整合并分析输入：** 根据`articles/{keyword}/super_outline.md`初级大纲和用户要求。

1. 【必要】搜索和分析网络内容，对比初级大纲并找出不足和缺失部分并使用深度搜索模式：

   - 查找最新的在线资源（如前10名谷歌文章、行业报告、X上的讨论，反映截至当前年份的信息）和竞争对手内容，以确定他们涵盖的主题、数据或案例研究
   - 特别搜索论坛（Reddit、Quora）、用户评论和初学者教程，了解：痛点是什么？目标读者对[主题]一致误解或发现特别困难的是什么？专家很少提及的"不成文规则"或"我希望一开始就知道的事情"是什么？初学者是否有"失败尝试"或"常见挫折"，而当前热门文章未充分提供简单解决方案？

2. 整个大纲结构应优先解答用户的核心问题和需求，然后是高级应该和优化等其他方面的标题。

3. 【必要】在大纲最后附加在研究过程中发现的**SEO NLP和长尾关键词**综合列表。这些应包括主要关键词和语义变体。

**引言、结论和常见问题：**
根据要求完成这3个部分：
- **引言**（100至250字）：
  基于New_article\info_aia.md中的开场策略撰写文章引言，包括一个过渡句，将开场引入内容，同时明确目的并保持参与度，然后以简明的论点总结文章价值并鼓励读者继续阅读。

- **结论**（100至250字）：
  总结要点，强调价值主张（例如，"现在你可以随时随地欣赏音乐"），并包含号召性用语（例如，"在评论中分享你的技巧"）。

- **常见问题**（100至200字）：
  保持3-5个常见问题，每个问题都以一句话简明回答。

【必要】**优先推荐解决方案**：
遵循`New_article/info_aia.md`中指定的产品推荐指南，确保适当的产品放置和推广。

【必要】**文章整体字数控制**：
根据用户指定的总字数要求，为每个H2和H3标题合理分配内容篇幅，严格遵守所有章节字数总和不超过用户限制(超过就必须要重新调整章节字数)。在每个标题后标注具体字数目标，便于后续内容创作时参考。

[Required][Required]为当前步骤提供一份简明的完成报告，保存为step3_compeletion_report，列出：
- 当前步骤已完成的任务
- 当前阶段是否有任何遗漏或者偏离原计划的部分

【重要节点】文章大纲完成后，等待用户检查确定。再进行下一步。

## 第2部分：文章起草和完善

### 步骤4：生成文章初稿
将初稿以MD格式保存为`articles/{keyword}/first_draft.md`。

**生成章节草稿具体步骤：**
【必要】初稿撰写是根据最终大纲的计划`articles/{keyword}/final_outline.md`并遵循`New_article\hl.md`的拟人化写作的指令来为大纲中的每个章节创建真实、吸引人的内容，最大化模拟自然人类写作模式。
  
**应用SEO：**
   - 【必要】在整个标题和内容中自然整合`final_outline.md`中的**SEO NLP和长尾关键词**，同时保持文章整体的可读性。不要强制塞入关键字破坏句子流畅性。

   - **EEAT实施：**【必要】
   - **经验：**
      - 分享来自广泛测试和实际实施的第一手经验，详细说明遇到的具体挑战和通过实践开发的经验证明的解决方案
      - 通过在实际执行期间记录操作工作流程，展示真实设置过程和故障排除场景
   
   - **专业知识：**
      - 使用日常类比分解技术概念（例如，"把比特率想象成水管 - 更大的管道携带更多数据，就像更宽的管道携带更多水"）
      - 提到产品和服务时候，要用可信来源（官方文档、专家访谈）支持解释，同时保持语言易于理解，并添加链接方便读者点击
   
   - **权威性：**
      - 用专业研究、研究和数据支持主张，根据需要在整篇文章中包含这些证据以增强读者可信度
      - 让内容由行业专家审查并验证，并适当归属（例如，"专家评审：[姓名]，有10+年经验的认证音频工程师"）。在相关部分末尾包含此专家验证声明
   
   - **可信度：**
      - 在文章末尾添加引人入胜的号召性用语（例如，"对您的设置有疑问？在下方分享您的经验！"或"哪种方法最适合您？请在评论中告诉我们！"）以鼓励读者互动，同时保持多样性

   - **内容元素** 【必要】
   - 根据内容需求优先考虑这些元素：
      - 比较表格（分析2+种替代方案时）
      - 关键要点/警告的突出显示框
      - 带有视觉指标的快速提示
      - 来自已验证用户/专家的引用
      - 支持复杂解释的视觉辅助工具
      - 展示实际结果的成功案例
      - 关键风险的警告说明
      - 明确增值的互动元素
   - 每篇文章至少使用4种不同元素
   - 与留白平衡，防止视觉过载

**链接要求:**
1. 为文章中所有产品名称、服务和技术术语添加相关权威外链
2. 每个链接在文章中只出现一次
3. 使用描述性锚文本（不要使用"点击这里"之类的泛泛文本）
4. 优先链接顺序:
   - 官方网站（产品、服务）
   - 权威百科或教育网站（技术概念）
   - 行业公认的权威资源（研究、统计数据）
5. 必须添加链接的元素:
   - 主要产品/服务名称
   - 专业技术术语
   - 第三方工具和软件
   - 任何非通用硬件设备
6. 链接密度控制在文章每500字约3-5个链接

[Required][Required]为当前步骤提供一份简明的完成报告，保存为step4_compeletion_report，列出：
- 当前步骤已完成的任务
- 当前阶段是否有任何遗漏或者偏离原计划的部分

【重要节点】初稿完成后，等待用户检查确定。再进行下一步。

### 步骤5：整合和优化文章
将审核过的终文章保存至`articles/{keyword}/final_article.md`。

1. **模仿人类写作检查：**【必要】
   - 根据`New_article\hl.md`指南审查`first_draft.md`中的所有章节。
    **根据以下步骤进行检查和修改：**

   [ ] 标题是否人性化、吸引人？

   [ ] 短句是否够多？长短句搭配是否自然？

   [ ] 对话短语用得是否自然、不刻意？

   [ ] 有无真情实感、个人色彩？

   [ ] 是否完全避免了禁用词和短语？

   [ ] 语气是否从头到尾都友好自然，像聊天一样？

   [ ] 阅读起来节奏是否流畅？

   [ ] 整体感觉是否像一个真实的朋友在热心分享，而不是AI模仿？

   [ ] 如果感觉生硬，尝试增加更短的句子、口语化表达或个人感悟。

   [ ] 结尾是否引导读者互动（例如：“你怎么看？”、“欢迎留言分享你的经验！”）？

2. **初学者友好验证清单：（不在文章中显示检查结果）** 【必要】
   根据以下的清单审查`first_draft.md`中的所有章节
   - [ ] 包含至少1个"其他地方未提及的教程细节"或"初学者陷阱警告"（来自原始或新价值维度）
   - [ ] 警告1个"常见初学者错误"并提供解决方案（可能来自"关键'不要做什么'"）
   - [ ] 提供"最简单但足够"的解决方案（可能来自"最低可行理解"或"简化解决方案"）
   - [ ] 解释"为什么这样做"而不仅仅是"如何做"
   - [ ] 解决初学者关注点（如"难吗？"，"免费版本够用吗？"，"安全吗？"，"需要多长时间？"）
   - [ ] 包含至少2个基于实际使用经验的具体提示
   - [ ] 使用具体数据或示例展示实际结果
   - [ ] 提供基于场景的使用建议（来自原始或"证明努力的价值"）
   - [ ] 是否为产品，名词解释提供链接，方便读者点击

3. **对文章进行全面的编辑审查，检查：**【必要】
   - 语法、拼写和排版错误
   - 验证逻辑流程和一致性，同时消除冗余、重叠或离题内容
   - 并通过搜索官方网站检查文章中的所有出现的价格和技术细节，确保准确性。
   - 检查按要求是否包含外部链接
   - 所有Markdown标题（例如，##，### 标题）必须单独成行，前后有空行

4. **文章字数调整** 【必要】
   - 使用`mcp-wordcounter`检查文章字数，如果文章字数超出限制（按顺序移除下面章节）：
      1. 常见问题解答和解释 → 检查字数
      2. 非必要示例 → 检查字数
      3. 核心内容精简 → 检查字数
      4. 重复直至达到目标
   - 如果低于限制（按顺序添加下面章节）：
      1. 可操作步骤 → 检查字数
      2. 实用示例 → 检查字数
      3. 术语解释 → 检查字数
      4. 重复直至达到目标
   - 每次添加或者删减超过300字就重写检查一下文章字数判断是否满足用户要求。

[Required]为当前步骤提供一份简明的完成报告，保存为step5_compeletion_report，列出：
- 当前步骤已完成的任务
- 对初稿的检查和优化，显示更改前后的版本对比，并提供更改的原因
- 当前阶段是否有任何遗漏或者偏离原计划的部分



## 第三部分：SEO + 图片提示生成说明
### 步骤6: SEO标题+元描述生成
将SEO元数据和图片提示词保存为：`articles/{keyword}/seo_metadata_images.md`

**阅读最终文章并生成5个SEO标题+元描述对：**
   
   对于每一对：
   - 标题（50-60个字符，包括空格）：
     * 将主关键词放在前3个词中
     * 至少包含一个：数字、好处、力量词或价值主张
   
   - 元描述（120-150个字符，包括空格）：
     * 主动语态
     * [问题/用例] + [解决方案] + [CTA]
     * 自然、情感化且具体
     * 直接放在其相应标题下方

使用`mcp-wordcounter`检查SEO标题（50-60字符）和元描述（120-150字符）是否符合长度要求。计算所有空格和标点符号。

### 图片提示词
为文章的特色图片和每个H2标题创建AI图像生成提示词：

**特色图片：**
- **直观表示** - 文章主题的直接视觉表现
  - 主要主题清晰定义
  - 特定构图和风格（照片写实/插图/平面/数字绘画）
  - 调色板（例如，为品牌相关内容使用Spotify绿色/黑色）
  - 输出格式：800×600px（4:3比例）

提取每个H2标题及其对应的段落内容，然后基于每个部分的关键词和核心内容生成适当的图像描述（提示）：
1. 从每个H2标题及其对应的段落内容中提取关键词
2. 为每个H2标题及其段落生成图像描述（提示）
3. 输出格式：600×400px（4:3比例）