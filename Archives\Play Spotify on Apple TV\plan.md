# 文章创作执行计划 - Play Spotify on Apple TV

## 用户具体需求和目标

### 基本信息
- **文章主题**: Play Spotify on Apple TV
- **SEO关键词**: Play Spotify on Apple TV
- **文章长度**: 1600字（不能少，最多可超出20%，即最多1920字）
- **语言**: 英文
- **时间框架**: 基于2025年6月的最新数据、趋势和案例研究
- **目标受众**: 音乐爱好者和创作者，专注于下载、编辑和分享音乐以获得更好的音频体验

### 内容质量要求
- **Effort (努力程度)**: 内容中必须体现明显的人工成分和深度思考
- **Originality (原创性)**: 提供独特信息增量，避免全网内容的"炒冷饭"
- **Talent/Skill (专业能力)**: 展示作者在该领域的专业知识和实际经验
- **Accuracy (准确性)**: 确保事实准确，避免错误信息

### 信息增量要求
- 每篇文章必须包含至少3-5个其他文章未涵盖的独特观点或解决方案
- 基于实际使用经验的个人见解和试错故事
- 针对用户痛点的具体解决方案，而非泛泛而谈

### 推荐产品
- **Cinch Audio Recorder** ($25.99 USD)
- 官方产品页面：https://www.cinchsolution.com/cinch-audio-recorder/
- 必须遵循产品推荐指南和写作原则

## 需要执行的所有步骤详细清单

### 步骤1：基础研究和大纲生成
1. **提取参考URL的标题结构**
   - 分析4个参考URL的H2-H4标题
   - 合并整理类似标题
   - 创建初步框架大纲

2. **竞品内容空白分析**
   - 深度分析Google搜索结果前10名文章
   - 识别现有内容的3-5个明显不足或遗漏点
   - 记录用户在Reddit、Quora等平台提出但主流文章未解决的问题

3. **独特价值点挖掘**
   - 找出至少3个"我希望早知道的事"类型的实用建议
   - 识别初学者常犯但教程很少提及的错误
   - 发现专家级用户才知道的隐藏技巧或最佳实践

### 步骤2：大纲优化和字数分配
1. **智能字数分配**（基于1600字目标）
   - Introduction: 128-160字 (8-10%)
   - 核心推荐章节(Cinch Audio Recorder): 320-400字 (20-25%)
   - 主要内容章节: 560-640字 (35-40%)
   - 支撑章节: 400-480字 (25-30%)
   - Conclusion + FAQ: 192字 (12%)

2. **大纲结构优化**
   - 优先解答用户核心问题和需求
   - 为每个H2章节准备人工经验要素
   - 附加SEO NLP和长尾关键词列表

### 步骤3：内容创作
1. **引言撰写**（使用策略A：Surprising Statistic/Fact Opening）
2. **主体内容创作**
   - 遵循字数分配
   - 整合Cinch Audio Recorder推荐
   - 包含个人经验和试错故事
3. **结论和FAQ撰写**

### 步骤4：SEO内容生成
1. **SEO标题和元描述**
2. **Featured image图片提示词**
3. **保存至seo_metadata_images.md**

### 步骤5：质量检查
1. **字数精确控制**（1600-1920字范围）
2. **拟人化写作检查**（参照hl.md要求）
3. **AI语言和句子结构检查**
4. **内链和外链有效性检查**
5. **相关图片添加检查**

## 每个步骤的完成标准和检查点

### 步骤1完成标准
- [ ] 成功提取并分析4个参考URL的标题结构
- [ ] 识别至少3-5个现有内容的不足点
- [ ] 挖掘至少3个独特价值点
- [ ] 创建super_outline.md文件

### 步骤2完成标准
- [ ] 字数分配总和在1520字以内（95%上限）
- [ ] 每个标题标注具体字数目标
- [ ] 核心推荐章节获得20-25%字数分配
- [ ] 创建final_outline.md文件

### 步骤3完成标准
- [ ] 文章总字数在1600-1920字范围内
- [ ] 包含至少3个个人经验案例
- [ ] 正确整合Cinch Audio Recorder推荐
- [ ] 创建first_draft.md文件

### 步骤4完成标准
- [ ] 生成5个SEO标题选项
- [ ] 创建对应的元描述
- [ ] 生成featured image提示词
- [ ] 创建seo_metadata_images.md文件

### 步骤5完成标准
- [ ] 字数精确控制在要求范围内
- [ ] 通过拟人化写作检查
- [ ] 无明显AI语言痕迹
- [ ] 所有链接有效性验证
- [ ] 相关图片正确添加

## 预期输出文件清单

1. **plan.md** - 本执行计划文件
2. **super_outline.md** - 初步大纲文件
3. **final_outline.md** - 最终优化大纲文件
4. **first_draft.md** - 文章初稿文件
5. **seo_metadata_images.md** - SEO元数据和图片提示词文件

## 项目时间线
- 步骤1-2：大纲生成和优化
- 步骤3：内容创作
- 步骤4：SEO内容生成
- 步骤5：质量检查和最终优化

## 成功指标
- 文章字数：1600-1920字
- 内容质量评分：95+分
- 独特价值点：至少3个
- 用户体验：解决核心痛点
- SEO优化：完整的元数据和关键词覆盖
