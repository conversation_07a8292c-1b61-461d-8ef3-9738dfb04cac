# 文章创作执行计划

## 用户需求和目标
- **文章主题**: Download and Record BBC Sounds
- **SEO关键词**: Download and Record BBC Sounds
- **文章长度**: 1600字（可超出20%，最多1920字）
- **语言**: 英文
- **时间框架**: 基于2025年6月的最新数据和趋势
- **目标受众**: 音乐爱好者和创作者，专注于下载、编辑和分享音乐以获得更好的音频体验
- **开头策略**: A (惊人统计/事实开头)
- **推荐产品**: Cinch Audio Recorder

## 四大内容质量评估维度
1. **Effort (努力程度)**: 体现明显的人工成分和深度思考
2. **Originality (原创性)**: 提供独特信息增量，避免"炒冷饭"
3. **Talent/Skill (专业能力)**: 展示作者在该领域的专业知识和实际经验
4. **Accuracy (准确性)**: 确保事实准确，避免错误信息

## 信息增量要求
- 包含至少3-5个其他文章未涵盖的独特观点或解决方案
- 基于实际使用经验的个人见解和试错故事
- 针对用户痛点的具体解决方案

## 执行步骤详细清单

### 步骤1: 基础"超级大纲"生成
- [ ] 提取参考URL的H2-H4级标题
- [ ] 合并整理类似标题
- [ ] 按层级结构重新组织
- [ ] 保存至 `super_outline.md`

### 步骤2: 创建最终文章大纲
- [ ] 基于超级大纲进行优化
- [ ] 进行竞品内容空白分析
- [ ] 挖掘独特价值点
- [ ] 添加人工经验要素
- [ ] 智能字数分配（1600字目标）
- [ ] 保存至 `final_outline.md`

### 步骤3: 创作初稿
- [ ] 基于最终大纲撰写文章
- [ ] 遵循拟人化写作风格
- [ ] 整合Cinch Audio Recorder产品推荐
- [ ] 保存至 `first_draft.md`

### 步骤4: 生成SEO内容
- [ ] 创建SEO标题和元描述
- [ ] 生成featured image提示词
- [ ] 保存至 `seo_metadata_images.md`

### 步骤5: 质量检查
- [ ] 验证字数控制在1600-1920字范围内
- [ ] 检查拟人化写作风格
- [ ] 确认所有步骤和要求完整执行

## 完成标准和检查点

### 超级大纲检查点
- 提取的标题数量充足且相关
- 标题合并逻辑清晰
- 层级结构合理

### 最终大纲检查点
- 包含至少3个竞品未涵盖的独特观点
- 每个H2章节准备了人工经验要素
- 识别并准备解决用户具体痛点
- 字数分配合理且总和在目标范围内

### 初稿检查点
- 文章结构完整（引言、正文、结论、FAQ）
- 产品推荐自然融入
- 拟人化写作风格一致
- 字数精确控制

### SEO内容检查点
- 标题和描述符合SEO最佳实践
- 图片提示词具体且相关

## 预期输出文件清单
1. `plan.md` - 执行计划文件
2. `super_outline.md` - 基础超级大纲
3. `final_outline.md` - 最终优化大纲
4. `first_draft.md` - 文章初稿
5. `seo_metadata_images.md` - SEO元数据和图片提示词

## 参考资源
- 用户需求文件: `New_article/info_aia.md`
- 产品指南: `New_article/car_guide.md`
- 拟人化写作风格: `New_article/hl.md`
- 参考URL列表:
  - https://www.viwizard.com/record-audio/download-and-record-bbc-sounds.html
  - https://www.for3.org/forums/forum/technical-forum/the-techie-board/1282735-downloading-recording-from-bbc-sounds
  - https://www.tuneskit.com/record-audio/record-bbc-radio.html
  - https://recorder.easeus.com/screen-recording-tips/record-bbc-radio.html
