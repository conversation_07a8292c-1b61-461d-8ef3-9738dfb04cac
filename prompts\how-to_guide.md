
TOPIC=[subject of the guide]
SKILLLEVEL=[beginner/intermediate/advanced]
FORMAT=[blog post/video script/infographic]

Research and list the top 5-10 most common questions or pain points people have when learning about or attempting TOPIC.~ Create an outline for the how-to guide, breaking TOPIC down into 5-7 main steps or sections. Ensure the complexity matches SKILLLEVEL.~ Write an engaging introduction that explains why TOPIC is important or beneficial, and what the reader will learn by the end of the guide.~ For each main step or section:
Provide a clear, concise explanation of what needs to be done.
Include any necessary warnings or preparatory steps.
Offer 2-3 tips or best practices related to this step.
If applicable, suggest tools or resources that can help with this step.
~ Identify potential challenges or common mistakes related to TOPIC. Create a troubleshooting section addressing these issues with solutions.
~ Develop a list of Frequently Asked Questions (FAQs) about TOPIC, complete with clear, concise answers.
~ Create a section on "Next Steps" or "Advanced Techniques" for readers who want to go beyond the basics of TOPIC.~ If TOPIC involves any technical terms or jargon, create a glossary defining these terms in simple language.
~ Based on FORMAT, suggest appropriate visual aids (e.g., diagrams, screenshots, or video timestamps) to supplement the written content at key points in the guide.~ Write a conclusion that summarizes the key points of the guide and encourages the reader to put their new knowledge into practice.
~ Compile all sections into a complete how-to guide formatted appropriately for FORMAT. Include a table of contents if it's a longer piece.
